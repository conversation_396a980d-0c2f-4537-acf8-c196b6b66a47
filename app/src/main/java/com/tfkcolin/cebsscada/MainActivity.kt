package com.tfkcolin.cebsscada

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.Router
import androidx.compose.material.icons.filled.Science
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel
import com.tfkcolin.cebsscada.communication.ui.CommunicationScreen
import com.tfkcolin.cebsscada.ui.permissions.BluetoothApp
import com.tfkcolin.cebsscada.ui.testing.MqttTestingScreen
import com.tfkcolin.cebsscada.ui.theme.CEBSSCADATheme
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        setContent {
            CEBSSCADATheme {
                CommunicationApp()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommunicationApp() {
    var selectedTab by remember { mutableStateOf(0) }

    val tabs = listOf(
        TabItem("Bluetooth", Icons.Default.Bluetooth),
        TabItem("MQTT", Icons.Default.Router),
        TabItem("Testing", Icons.Default.Science)
    )

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text("CEBS SCADA - Communication Hub") }
            )
        },
        bottomBar = {
            NavigationBar {
                tabs.forEachIndexed { index, tab ->
                    NavigationBarItem(
                        icon = { Icon(tab.icon, contentDescription = tab.title) },
                        label = { Text(tab.title) },
                        selected = selectedTab == index,
                        onClick = { selectedTab = index }
                    )
                }
            }
        }
    ) { innerPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(innerPadding)
        ) {
            when (selectedTab) {
                0 -> BluetoothApp()
                1 -> CommunicationScreen()
                2 -> MqttTestingScreen()
            }
        }
    }
}

data class TabItem(
    val title: String,
    val icon: ImageVector
)