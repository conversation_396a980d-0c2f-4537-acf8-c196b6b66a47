package com.tfkcolin.cebsscada.ui.bluetooth

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.tfkcolin.cebsscada.bluetooth.BluetoothState
import com.tfkcolin.cebsscada.bluetooth.BluetoothViewModel
import com.tfkcolin.cebsscada.bluetooth.DeviceType
import com.tfkcolin.cebsscada.bluetooth.ScanType

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BluetoothScreen(viewModel: BluetoothViewModel = hiltViewModel()) {
    val bluetoothState by viewModel.bluetoothState
    val discoveredDevices = viewModel.discoveredDevices
    val messages = viewModel.messages
    val isScanning by viewModel.isScanning
    val isBluetoothEnabled by viewModel.isBluetoothEnabled
    val debugLogs = viewModel.debugLogs
    val gattServices = viewModel.gattServices

    LazyColumn (
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        item {
            // Bluetooth status bar
            BluetoothStatusBar(
                isEnabled = isBluetoothEnabled,
                state = bluetoothState,
                onEnable = { viewModel.enableBluetooth() }
            )
            Spacer(modifier = Modifier.height(16.dp))
        }

        item {
            // Device list and controls
            when (val currentState = bluetoothState) {
                is BluetoothState.Connected -> {
                    val deviceWrapper = discoveredDevices.find { it.address == currentState.device.address }
                    if (deviceWrapper?.type == DeviceType.BLE) {
                        BleCommunicationScreen(
                            device = currentState.device,
                            services = gattServices,
                            onDisconnect = { viewModel.disconnect() },
                            onReadCharacteristic = { viewModel.readCharacteristic(it) },
                            onWriteCharacteristic = { characteristic, value -> viewModel.writeCharacteristic(characteristic, value) },
                            onEnableNotifications = { viewModel.enableNotifications(it) },
                            onDisableNotifications = { viewModel.disableNotifications(it) }
                        )
                    } else {
                        ClassicCommunicationScreen(
                            device = currentState.device,
                            messages = messages,
                            onSendMessage = { viewModel.sendMessage(it) },
                            onDisconnect = { viewModel.disconnect() },
                            onClearMessages = { viewModel.clearMessages() }
                        )
                    }
                }

                else -> {
                    DeviceListScreen(
                        devices = discoveredDevices,
                        isScanning = isScanning,
                        onScanStart = { viewModel.startScan(it) },
                        onScanStop = { viewModel.stopScan() },
                        onDeviceClick = { viewModel.connect(it) }
                    )
                }
            }
        }

        // Debug logs section
        item {
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = "Debug Logs",
                style = MaterialTheme.typography.headlineSmall
            )
            Spacer(modifier = Modifier.height(8.dp))
        }

        if (debugLogs.isEmpty()) {
            item {
                Text(
                    text = "No debug logs yet",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                )
            }
        } else {
            items(debugLogs) { log ->
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 2.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
                ) {
                    Text(
                        text = log,
                        style = MaterialTheme.typography.bodySmall,
                        modifier = Modifier.padding(8.dp)
                    )
                }
            }
        }

        item {
            Spacer(modifier = Modifier.height(8.dp))
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Button(onClick = { viewModel.clearDebugLogs() }) {
                    Text("Clear Logs")
                }
                Button(onClick = { viewModel.startScan(ScanType.BLE) }) {
                    Text("Test Scan BLE")
                }
                Button(onClick = { viewModel.startScan(ScanType.CLASSIC) }) {
                    Text("Test Scan Classic")
                }
            }
        }
    }
}
