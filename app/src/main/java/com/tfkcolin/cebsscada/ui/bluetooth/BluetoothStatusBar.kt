package com.tfkcolin.cebsscada.ui.bluetooth

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bluetooth
import androidx.compose.material.icons.filled.BluetoothDisabled
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.tfkcolin.cebsscada.bluetooth.BluetoothState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun BluetoothStatusBar(
    isEnabled: Boolean,
    state: BluetoothState,
    onEnable: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Bluetooth Status",
                    style = MaterialTheme.typography.headlineSmall
                )
                
                Icon(
                    imageVector = if (isEnabled) Icons.Default.Bluetooth else Icons.Default.BluetoothDisabled,
                    contentDescription = if (isEnabled) "Bluetooth Enabled" else "Bluetooth Disabled",
                    tint = if (isEnabled) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.error
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            when (state) {
                is BluetoothState.Error -> {
                    Text(
                        text = state.message,
                        color = MaterialTheme.colorScheme.error
                    )
                }
                is BluetoothState.Connecting -> {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Connecting...")
                    }
                }
                is BluetoothState.Connected -> {
                    Text(
                        text = "Connected to ${state.device.name ?: "Unknown Device"}",
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                else -> {
                    Text(
                        text = if (isEnabled) "Ready to connect" else "Bluetooth disabled",
                        color = if (isEnabled) MaterialTheme.colorScheme.onSurface else MaterialTheme.colorScheme.error
                    )
                }
            }
            
            if (!isEnabled) {
                Spacer(modifier = Modifier.height(8.dp))
                Button(
                    onClick = onEnable,
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("Enable Bluetooth")
                }
            }
        }
    }
}
