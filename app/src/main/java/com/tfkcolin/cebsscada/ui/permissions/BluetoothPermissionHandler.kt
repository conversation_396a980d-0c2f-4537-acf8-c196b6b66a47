package com.tfkcolin.cebsscada.ui.permissions

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat

@Composable
fun BluetoothPermissionHandler(
    onPermissionsGranted: () -> Unit,
    onPermissionsDenied: () -> Unit
) {
    val context = LocalContext.current
    
    val bluetoothPermissions = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
        listOf(
            Manifest.permission.BLUETOOTH_SCAN,
            Manifest.permission.BLUETOOTH_CONNECT,
            Manifest.permission.BLUETOOTH_ADVERTISE
        )
    } else {
        listOf(
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            Manifest.permission.ACCESS_FINE_LOCATION,
            Manifest.permission.ACCESS_COARSE_LOCATION
        )
    }
    
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        android.util.Log.d("BluetoothPermissionHandler", "Permission results: $permissions")
        val allGranted = permissions.all { it.value }
        android.util.Log.d("BluetoothPermissionHandler", "All permissions granted: $allGranted")
        if (allGranted) {
            onPermissionsGranted()
        } else {
            onPermissionsDenied()
        }
    }
    
    LaunchedEffect(Unit) {
        android.util.Log.d("BluetoothPermissionHandler", "Checking permissions: $bluetoothPermissions")
        val hasAllPermissions = bluetoothPermissions.all {
            val granted = ContextCompat.checkSelfPermission(context, it) == PackageManager.PERMISSION_GRANTED
            android.util.Log.d("BluetoothPermissionHandler", "Permission $it granted: $granted")
            granted
        }
        android.util.Log.d("BluetoothPermissionHandler", "Has all permissions: $hasAllPermissions")

        if (hasAllPermissions) {
            onPermissionsGranted()
        } else {
            permissionLauncher.launch(bluetoothPermissions.toTypedArray())
        }
    }
}

@Composable
fun PermissionDeniedScreen(
    onRetry: () -> Unit,
    onOpenSettings: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            imageVector = Icons.Default.Warning,
            contentDescription = "Warning",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "Bluetooth Permissions Required",
            style = MaterialTheme.typography.headlineSmall
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = "This app needs Bluetooth permissions to connect with devices. Please grant the necessary permissions.",
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(24.dp))
        
        Row {
            Button(onClick = onRetry) {
                Text("Retry")
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            OutlinedButton(onClick = onOpenSettings) {
                Text("Open Settings")
            }
        }
    }
}

@Composable
fun BluetoothApp() {
    val context = LocalContext.current
    var hasPermissions by remember { mutableStateOf(false) }
    var showPermissionDenied by remember { mutableStateOf(false) }
    
    if (hasPermissions) {
        com.tfkcolin.cebsscada.ui.bluetooth.BluetoothScreen()
    } else {
        BluetoothPermissionHandler(
            onPermissionsGranted = { hasPermissions = true },
            onPermissionsDenied = { showPermissionDenied = true }
        )
        
        if (showPermissionDenied) {
            PermissionDeniedScreen(
                onRetry = { showPermissionDenied = false },
                onOpenSettings = {
                    val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                        data = Uri.fromParts("package", context.packageName, null)
                        addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    }
                    context.startActivity(intent)
                }
            )
        }
    }
}
