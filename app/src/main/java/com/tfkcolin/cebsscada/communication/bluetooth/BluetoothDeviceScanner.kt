package com.tfkcolin.cebsscada.communication.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice as AndroidBluetoothDevice
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.util.Log
import androidx.annotation.RequiresPermission
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.DeviceType
import com.tfkcolin.cebsscada.communication.interfaces.DeviceScanner
import com.tfkcolin.cebsscada.communication.models.BluetoothDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import java.util.concurrent.atomic.AtomicBoolean
import javax.inject.Inject

/**
 * Bluetooth device scanner implementing the new architecture
 */
class BluetoothDeviceScanner @Inject constructor(
    private val context: Context,
    private val bluetoothAdapter: BluetoothAdapter?
) : DeviceScanner {
    
    companion object {
        private const val TAG = "BluetoothDeviceScanner"
    }
    
    override val supportedProtocol: CommunicationProtocol = CommunicationProtocol.BLUETOOTH_CLASSIC
    
    private val _discoveredDevices = MutableStateFlow<List<CommunicationDevice>>(emptyList())
    override val discoveredDevices: Flow<List<CommunicationDevice>> = _discoveredDevices.asStateFlow()
    
    private val _isScanning = MutableStateFlow(false)
    override val isScanning: Flow<Boolean> = _isScanning.asStateFlow()
    
    private val _scanningErrors = MutableSharedFlow<String>()
    override val scanningErrors: Flow<String> = _scanningErrors.asSharedFlow()
    
    private val _isScanningActive = AtomicBoolean(false)
    override val isScanningActive: Boolean
        get() = _isScanningActive.get()
    
    private var bluetoothLeScanner: BluetoothLeScanner? = null
    private var scanCallback: ScanCallback? = null
    private val deviceMap = mutableMapOf<String, CommunicationDevice>()
    
    private val bluetoothReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                AndroidBluetoothDevice.ACTION_FOUND -> {
                    val device = intent.getParcelableExtra<AndroidBluetoothDevice>(AndroidBluetoothDevice.EXTRA_DEVICE)
                    val rssi = intent.getShortExtra(AndroidBluetoothDevice.EXTRA_RSSI, 0).toInt()
                    device?.let { addDiscoveredDevice(it, DeviceType.BLUETOOTH_CLASSIC, rssi) }
                }
                BluetoothAdapter.ACTION_DISCOVERY_FINISHED -> {
                    _isScanning.value = false
                    _isScanningActive.set(false)
                }
                BluetoothAdapter.ACTION_DISCOVERY_STARTED -> {
                    _isScanning.value = true
                    _isScanningActive.set(true)
                }
            }
        }
    }
    
    init {
        bluetoothLeScanner = bluetoothAdapter?.bluetoothLeScanner
        registerBluetoothReceiver()
    }
    
    @RequiresPermission(allOf = [Manifest.permission.BLUETOOTH_SCAN, Manifest.permission.BLUETOOTH_CONNECT])
    override suspend fun startScan(options: Map<String, Any>) {
        if (_isScanningActive.get()) {
            return
        }
        
        if (bluetoothAdapter == null) {
            _scanningErrors.emit("Bluetooth adapter not available")
            return
        }
        
        if (!bluetoothAdapter.isEnabled) {
            _scanningErrors.emit("Bluetooth is not enabled")
            return
        }
        
        _isScanningActive.set(true)
        _isScanning.value = true
        
        // Start classic Bluetooth discovery
        bluetoothAdapter.startDiscovery()
        
        // Start BLE scan
        startBLEScan(options)
        
        Log.d(TAG, "Started Bluetooth device scanning")
    }
    
    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    private fun startBLEScan(options: Map<String, Any>) {
        if (bluetoothLeScanner == null) {
            Log.w(TAG, "BLE scanner not available")
            return
        }
        
        scanCallback = object : ScanCallback() {
            override fun onScanResult(callbackType: Int, result: ScanResult) {
                super.onScanResult(callbackType, result)
                addDiscoveredDevice(result.device, DeviceType.BLUETOOTH_BLE, result.rssi)
            }
            
            override fun onBatchScanResults(results: MutableList<ScanResult>) {
                super.onBatchScanResults(results)
                results.forEach { result ->
                    addDiscoveredDevice(result.device, DeviceType.BLUETOOTH_BLE, result.rssi)
                }
            }
            
            override fun onScanFailed(errorCode: Int) {
                super.onScanFailed(errorCode)
                kotlinx.coroutines.runBlocking {
                    _scanningErrors.emit("BLE scan failed with error code: $errorCode")
                }
            }
        }
        
        val scanSettings = ScanSettings.Builder()
            .setScanMode(ScanSettings.SCAN_MODE_LOW_POWER)
            .build()
        
        bluetoothLeScanner?.startScan(emptyList(), scanSettings, scanCallback)
    }
    
    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    override suspend fun stopScan() {
        if (!_isScanningActive.get()) {
            return
        }
        
        // Stop classic Bluetooth discovery
        bluetoothAdapter?.cancelDiscovery()
        
        // Stop BLE scan
        scanCallback?.let { callback ->
            bluetoothLeScanner?.stopScan(callback)
        }
        scanCallback = null
        
        _isScanning.value = false
        _isScanningActive.set(false)
        
        Log.d(TAG, "Stopped Bluetooth device scanning")
    }
    
    override suspend fun clearDevices() {
        deviceMap.clear()
        _discoveredDevices.value = emptyList()
    }
    
    override suspend fun getCurrentDevices(): List<CommunicationDevice> {
        return _discoveredDevices.value
    }
    
    override suspend fun cleanup() {
        stopScan()
        unregisterBluetoothReceiver()
    }
    
    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    private fun addDiscoveredDevice(androidDevice: AndroidBluetoothDevice, deviceType: DeviceType, rssi: Int) {
        try {
            val deviceName = androidDevice.name ?: "Unknown Device"
            val deviceAddress = androidDevice.address
            
            if (deviceMap.containsKey(deviceAddress)) {
                return // Device already discovered
            }
            
            val bluetoothDevice = BluetoothDevice(
                id = deviceAddress,
                name = deviceName,
                address = deviceAddress,
                deviceType = deviceType,
                rssi = rssi,
                isBonded = androidDevice.bondState == AndroidBluetoothDevice.BOND_BONDED,
                serviceUuids = androidDevice.uuids?.map { it.toString() } ?: emptyList()
            )
            
            deviceMap[deviceAddress] = bluetoothDevice
            _discoveredDevices.value = deviceMap.values.toList()
            
            Log.d(TAG, "Discovered ${deviceType.name} device: $deviceName ($deviceAddress)")
        } catch (e: SecurityException) {
            Log.e(TAG, "Security exception while processing discovered device", e)
            kotlinx.coroutines.runBlocking {
                _scanningErrors.emit("Permission denied while processing device")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing discovered device", e)
            kotlinx.coroutines.runBlocking {
                _scanningErrors.emit("Error processing device: ${e.message}")
            }
        }
    }
    
    private fun registerBluetoothReceiver() {
        val filter = IntentFilter().apply {
            addAction(AndroidBluetoothDevice.ACTION_FOUND)
            addAction(BluetoothAdapter.ACTION_DISCOVERY_FINISHED)
            addAction(BluetoothAdapter.ACTION_DISCOVERY_STARTED)
        }
        context.registerReceiver(bluetoothReceiver, filter)
    }
    
    private fun unregisterBluetoothReceiver() {
        try {
            context.unregisterReceiver(bluetoothReceiver)
        } catch (e: IllegalArgumentException) {
            // Receiver was not registered
            Log.w(TAG, "Bluetooth receiver was not registered")
        }
    }
}
