package com.tfkcolin.cebsscada.communication.ui

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.foundation.clickable
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.tfkcolin.cebsscada.communication.CommunicationProtocol
import com.tfkcolin.cebsscada.communication.ConnectionState
import com.tfkcolin.cebsscada.communication.models.CommunicationDevice
import com.tfkcolin.cebsscada.communication.models.CommunicationMessage
import com.tfkcolin.cebsscada.communication.models.TopicSubscription

/**
 * Main communication screen that works with all protocols
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommunicationScreen(
    viewModel: CommunicationViewModel = hiltViewModel()
) {
    val selectedProtocol by viewModel.selectedProtocol.collectAsState()
    val supportedProtocols by viewModel.supportedProtocols.collectAsState()
    val currentDevices by viewModel.currentDevices.collectAsState(initial = emptyList())
    val connectionState by viewModel.currentConnectionState.collectAsState(initial = ConnectionState.DISCONNECTED)
    val connectedDevice by viewModel.currentConnectedDevice.collectAsState(initial = null)
    val messages by viewModel.messages.collectAsState()
    val isScanning by viewModel.isScanning.collectAsState()

    var showConfigDialog by remember { mutableStateOf(false) }
    
    var selectedTabIndex by remember { mutableIntStateOf(0) }
    val tabs = listOf("Devices", "Messages", "Settings")
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
    ) {
        // Protocol selector
        ProtocolSelector(
            selectedProtocol = selectedProtocol,
            supportedProtocols = supportedProtocols,
            onProtocolSelected = viewModel::selectProtocol
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Connection status
        ConnectionStatusCard(
            protocol = selectedProtocol,
            connectionState = connectionState,
            connectedDevice = connectedDevice,
            onConnect = { /* Handle in device list */ },
            onDisconnect = viewModel::disconnect
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Tab row
        TabRow(selectedTabIndex = selectedTabIndex) {
            tabs.forEachIndexed { index, title ->
                Tab(
                    selected = selectedTabIndex == index,
                    onClick = { selectedTabIndex = index },
                    text = { Text(title) }
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // Tab content
        when (selectedTabIndex) {
            0 -> DevicesTab(
                protocol = selectedProtocol,
                devices = currentDevices,
                isScanning = isScanning,
                connectionState = connectionState,
                onStartScan = viewModel::startDiscovery,
                onStopScan = viewModel::stopDiscovery,
                onConnect = viewModel::connect,
                onRemoveDevice = viewModel::removeManualDevice,
                supportsScanning = viewModel.supportsScanning(selectedProtocol),
                supportsManualConfig = viewModel.supportsManualConfiguration(selectedProtocol),
                onShowConfigDialog = { showConfigDialog = true }
            )
            1 -> MessagesTab(
                protocol = selectedProtocol,
                messages = messages.filter { it.protocol == selectedProtocol },
                connectionState = connectionState,
                onSendMessage = viewModel::sendText,
                onClearMessages = viewModel::clearMessages,
                supportsTopics = viewModel.supportsTopics(selectedProtocol),
                subscriptions = if (viewModel.supportsTopics(selectedProtocol)) {
                    viewModel.getSubscriptions().collectAsState(initial = emptyList()).value
                } else emptyList(),
                onSubscribe = viewModel::subscribe,
                onUnsubscribe = viewModel::unsubscribe
            )
            2 -> SettingsTab(
                protocol = selectedProtocol,
                connectionStats = viewModel.getConnectionStats()
            )
        }
    }

    // Show configuration dialog
    if (showConfigDialog) {
        DeviceConfigurationDialog(
            protocol = selectedProtocol,
            onDismiss = { showConfigDialog = false },
            onDeviceConfigured = { device ->
                viewModel.addManualDevice(device)
                showConfigDialog = false
            }
        )
    }
}

@Composable
fun ProtocolSelector(
    selectedProtocol: CommunicationProtocol,
    supportedProtocols: List<CommunicationProtocol>,
    onProtocolSelected: (CommunicationProtocol) -> Unit
) {
    var expanded by remember { mutableStateOf(false) }
    
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "Communication Protocol",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Bold
            )
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Box {
                OutlinedTextField(
                    value = selectedProtocol.name.replace("_", " "),
                    onValueChange = { },
                    readOnly = true,
                    trailingIcon = {
                        IconButton(onClick = { expanded = !expanded }) {
                            Icon(
                                imageVector = if (expanded) Icons.Default.ExpandLess else Icons.Default.ExpandMore,
                                contentDescription = "Expand"
                            )
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable { expanded = !expanded }
                )

                DropdownMenu(
                    expanded = expanded,
                    onDismissRequest = { expanded = false }
                ) {
                    supportedProtocols.forEach { protocol ->
                        DropdownMenuItem(
                            text = { Text(protocol.name.replace("_", " ")) },
                            onClick = {
                                onProtocolSelected(protocol)
                                expanded = false
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun ConnectionStatusCard(
    protocol: CommunicationProtocol,
    connectionState: ConnectionState,
    connectedDevice: CommunicationDevice?,
    onConnect: () -> Unit,
    onDisconnect: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Connection Status",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
                
                Icon(
                    imageVector = when (connectionState) {
                        ConnectionState.CONNECTED -> Icons.Default.CheckCircle
                        ConnectionState.CONNECTING -> Icons.Default.Refresh
                        ConnectionState.DISCONNECTING -> Icons.Default.Refresh
                        ConnectionState.ERROR -> Icons.Default.Error
                        ConnectionState.RECONNECTING -> Icons.Default.Refresh
                        ConnectionState.DISCONNECTED -> Icons.Default.RadioButtonUnchecked
                    },
                    contentDescription = "Connection Status",
                    tint = when (connectionState) {
                        ConnectionState.CONNECTED -> MaterialTheme.colorScheme.primary
                        ConnectionState.ERROR -> MaterialTheme.colorScheme.error
                        else -> MaterialTheme.colorScheme.onSurface
                    }
                )
            }
            
            Spacer(modifier = Modifier.height(8.dp))
            
            Text(
                text = when (connectionState) {
                    ConnectionState.CONNECTED -> "Connected to ${connectedDevice?.name ?: "Unknown"}"
                    ConnectionState.CONNECTING -> "Connecting..."
                    ConnectionState.DISCONNECTING -> "Disconnecting..."
                    ConnectionState.ERROR -> "Connection Error"
                    ConnectionState.RECONNECTING -> "Reconnecting..."
                    ConnectionState.DISCONNECTED -> "Disconnected"
                },
                style = MaterialTheme.typography.bodyMedium
            )
            
            if (connectionState == ConnectionState.CONNECTED) {
                Spacer(modifier = Modifier.height(8.dp))
                Button(
                    onClick = onDisconnect,
                    modifier = Modifier.align(Alignment.End)
                ) {
                    Text("Disconnect")
                }
            }
        }
    }
}

@Composable
fun DevicesTab(
    protocol: CommunicationProtocol,
    devices: List<CommunicationDevice>,
    isScanning: Boolean,
    connectionState: ConnectionState,
    onStartScan: () -> Unit,
    onStopScan: () -> Unit,
    onConnect: (CommunicationDevice) -> Unit,
    onRemoveDevice: (CommunicationDevice) -> Unit,
    supportsScanning: Boolean,
    supportsManualConfig: Boolean,
    onShowConfigDialog: () -> Unit
) {
    Column {
        // Header with action buttons
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Available Devices",
                style = MaterialTheme.typography.titleMedium
            )

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                if (supportsScanning) {
                    if (isScanning) {
                        Button(onClick = onStopScan) {
                            Icon(Icons.Default.Stop, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Stop Scan")
                        }
                    } else {
                        Button(onClick = onStartScan) {
                            Icon(Icons.Default.Search, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Start Scan")
                        }
                    }
                }

                if (supportsManualConfig) {
                    OutlinedButton(onClick = onShowConfigDialog) {
                        Icon(Icons.Default.Add, contentDescription = null)
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Add Device")
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(16.dp))
        
        if (devices.isEmpty() && !isScanning) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = when {
                            supportsScanning && supportsManualConfig -> "No devices found. Start scanning or add devices manually."
                            supportsScanning -> "No devices found. Start scanning to discover devices."
                            supportsManualConfig -> "Click 'Add Device' to configure devices manually."
                            else -> "No devices available for this protocol."
                        },
                        style = MaterialTheme.typography.bodyMedium
                    )

                    if (supportsManualConfig && !supportsScanning) {
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(onClick = onShowConfigDialog) {
                            Icon(Icons.Default.Add, contentDescription = null)
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("Add Device")
                        }
                    }
                }
            }
        } else {
            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(devices) { device ->
                    DeviceItem(
                        device = device,
                        connectionState = connectionState,
                        onConnect = { onConnect(device) },
                        onRemove = if (supportsManualConfig) { { onRemoveDevice(device) } } else null
                    )
                }
            }
        }
    }
}

@Composable
fun DeviceItem(
    device: CommunicationDevice,
    connectionState: ConnectionState,
    onConnect: () -> Unit,
    onRemove: (() -> Unit)? = null
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = device.name,
                    style = MaterialTheme.typography.titleSmall,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = device.address,
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = device.deviceType.name.replace("_", " "),
                    style = MaterialTheme.typography.bodySmall
                )
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                if (onRemove != null) {
                    IconButton(onClick = onRemove) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Remove Device",
                            tint = MaterialTheme.colorScheme.error
                        )
                    }
                }

                Button(
                    onClick = onConnect,
                    enabled = connectionState == ConnectionState.DISCONNECTED
                ) {
                    Text("Connect")
                }
            }
        }
    }
}

@Composable
fun MessagesTab(
    protocol: CommunicationProtocol,
    messages: List<CommunicationMessage>,
    connectionState: ConnectionState,
    onSendMessage: (String, String?) -> Unit,
    onClearMessages: () -> Unit,
    supportsTopics: Boolean,
    subscriptions: List<TopicSubscription>,
    onSubscribe: (String) -> Unit,
    onUnsubscribe: (String) -> Unit
) {
    var messageText by remember { mutableStateOf("") }
    var topicText by remember { mutableStateOf("") }
    var subscriptionTopic by remember { mutableStateOf("") }

    Column {
        // Message input section
        if (connectionState == ConnectionState.CONNECTED) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Send Message",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    if (supportsTopics) {
                        OutlinedTextField(
                            value = topicText,
                            onValueChange = { topicText = it },
                            label = { Text("Topic") },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true
                        )

                        Spacer(modifier = Modifier.height(8.dp))
                    }

                    OutlinedTextField(
                        value = messageText,
                        onValueChange = { messageText = it },
                        label = { Text("Message") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 3
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        Button(
                            onClick = {
                                if (messageText.isNotBlank()) {
                                    onSendMessage(
                                        messageText,
                                        if (supportsTopics && topicText.isNotBlank()) topicText else null
                                    )
                                    messageText = ""
                                }
                            },
                            enabled = messageText.isNotBlank() && (!supportsTopics || topicText.isNotBlank())
                        ) {
                            Text("Send")
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        // Subscriptions section (for MQTT)
        if (supportsTopics && connectionState == ConnectionState.CONNECTED) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Subscriptions",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedTextField(
                            value = subscriptionTopic,
                            onValueChange = { subscriptionTopic = it },
                            label = { Text("Topic to subscribe") },
                            modifier = Modifier.weight(1f),
                            singleLine = true
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Button(
                            onClick = {
                                if (subscriptionTopic.isNotBlank()) {
                                    onSubscribe(subscriptionTopic)
                                    subscriptionTopic = ""
                                }
                            },
                            enabled = subscriptionTopic.isNotBlank()
                        ) {
                            Text("Subscribe")
                        }
                    }

                    if (subscriptions.isNotEmpty()) {
                        Spacer(modifier = Modifier.height(8.dp))

                        subscriptions.forEach { subscription ->
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = subscription.topic,
                                    modifier = Modifier.weight(1f)
                                )

                                TextButton(
                                    onClick = { onUnsubscribe(subscription.topic) }
                                ) {
                                    Text("Unsubscribe")
                                }
                            }
                        }
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))
        }

        // Messages section
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "Messages (${messages.size})",
                style = MaterialTheme.typography.titleMedium
            )

            if (messages.isNotEmpty()) {
                TextButton(onClick = onClearMessages) {
                    Text("Clear")
                }
            }
        }

        Spacer(modifier = Modifier.height(8.dp))

        if (messages.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "No messages yet",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        } else {
            LazyColumn(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(8.dp),
                reverseLayout = true
            ) {
                items(messages.reversed()) { message ->
                    MessageItem(message = message)
                }
            }
        }
    }
}

@Composable
fun MessageItem(message: CommunicationMessage) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 1.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = message.direction.name,
                    style = MaterialTheme.typography.labelSmall,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
                        .format(java.util.Date(message.timestamp)),
                    style = MaterialTheme.typography.labelSmall
                )
            }

            if (message.topic != null) {
                Text(
                    text = "Topic: ${message.topic}",
                    style = MaterialTheme.typography.labelSmall
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            Text(
                text = message.contentAsString,
                style = MaterialTheme.typography.bodyMedium
            )
        }
    }
}

@Composable
fun SettingsTab(
    protocol: CommunicationProtocol,
    connectionStats: Map<CommunicationProtocol, Map<String, Any>>
) {
    val stats = connectionStats[protocol] ?: emptyMap()

    LazyColumn(
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Card(
                modifier = Modifier.fillMaxWidth(),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Connection Statistics",
                        style = MaterialTheme.typography.titleMedium,
                        fontWeight = FontWeight.Bold
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    stats.forEach { (key, value) ->
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween
                        ) {
                            Text(
                                text = key.replace("([A-Z])".toRegex(), " $1").trim(),
                                style = MaterialTheme.typography.bodyMedium
                            )
                            Text(
                                text = value.toString(),
                                style = MaterialTheme.typography.bodyMedium,
                                fontWeight = FontWeight.Bold
                            )
                        }

                        if (key != stats.keys.last()) {
                            Spacer(modifier = Modifier.height(4.dp))
                        }
                    }
                }
            }
        }
    }
}
