package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.app.Application
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattService
import android.content.BroadcastReceiver
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.ServiceConnection
import android.os.Build
import android.os.IBinder
import androidx.annotation.RequiresPermission
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import dagger.hilt.android.qualifiers.ApplicationContext
import java.util.UUID
import javax.inject.Inject

@HiltViewModel
class BluetoothViewModel @Inject constructor(
    @ApplicationContext private val context: Context
) : AndroidViewModel(context.applicationContext as Application) {
    private val bluetoothAdapter: BluetoothAdapter? = BluetoothAdapter.getDefaultAdapter()
    private val bleScanner: BLEScanner = BLEScanner(context) { debugLog ->
        addDebugLog(debugLog)
    }
    private var bluetoothService: BluetoothService? = null

    private val _bluetoothState = mutableStateOf<BluetoothState>(BluetoothState.Idle)
    val bluetoothState: State<BluetoothState> = _bluetoothState

    private val _discoveredDevices = mutableStateListOf<BluetoothDeviceWrapper>()
    val discoveredDevices: List<BluetoothDeviceWrapper> = _discoveredDevices

    private val _gattServices = mutableStateListOf<BluetoothGattService>()
    val gattServices: List<BluetoothGattService> = _gattServices

    private val _messages = mutableStateListOf<String>()
    val messages: List<String> = _messages

    private val _debugLogs = mutableStateListOf<String>()
    val debugLogs: List<String> = _debugLogs

    private val _isScanning = mutableStateOf(false)
    val isScanning: State<Boolean> = _isScanning

    private val _scanType = mutableStateOf(ScanType.NONE)
    val scanType: State<ScanType> = _scanType

    private val _isBluetoothEnabled = mutableStateOf(false)
    val isBluetoothEnabled: State<Boolean> = _isBluetoothEnabled

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            val binder = service as BluetoothService.LocalBinder
            bluetoothService = binder.getService()
            bluetoothService?.initialize()
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            bluetoothService = null
        }
    }

    private val bluetoothReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent?) {
            when (intent?.action) {
                BluetoothConstants.ACTION_CONNECTING -> {
                    addDebugLog("Connection state: CONNECTING")
                    _bluetoothState.value = BluetoothState.Connecting
                }
                BluetoothConstants.ACTION_CONNECTED -> {
                    val deviceAddress = intent.getStringExtra(BluetoothConstants.EXTRA_DEVICE_ADDRESS)
                    addDebugLog("Connection state: CONNECTED to device: $deviceAddress")
                    val device = _discoveredDevices.find { it.address == deviceAddress }?.device
                    device?.let {
                        _bluetoothState.value = BluetoothState.Connected(it)
                        addDebugLog("Updated state to Connected(${it.name ?: "Unknown"})")
                    } ?: addDebugLog("Warning: Connected device not found in discovered devices list")
                }
                BluetoothConstants.ACTION_DISCONNECTED -> {
                    addDebugLog("Connection state: DISCONNECTED")
                    _bluetoothState.value = BluetoothState.Idle
                    _gattServices.clear()
                }
                BluetoothConstants.ACTION_CONNECTION_FAILED -> {
                    val errorCode = intent.getIntExtra(BluetoothConstants.EXTRA_ERROR_CODE, BluetoothConstants.ERROR_NONE)
                    val errorMessage = intent.getStringExtra(BluetoothConstants.EXTRA_ERROR_MESSAGE) ?: "Unknown error"
                    addDebugLog("Connection state: FAILED - Error $errorCode: $errorMessage")
                    _bluetoothState.value = BluetoothState.Error(errorMessage, errorCode)
                }
                BluetoothConstants.ACTION_BLE_SERVICES_DISCOVERED -> {
                    _gattServices.clear()
                    bluetoothService?.getSupportedGattServices()?.let { _gattServices.addAll(it) }
                }
                BluetoothConstants.ACTION_MESSAGE_RECEIVED -> {
                    val message = intent.getStringExtra(BluetoothConstants.EXTRA_MESSAGE)
                    val data = intent.getByteArrayExtra(BluetoothConstants.EXTRA_DATA)
                    if (message != null) {
                        _messages.add("Received: $message")
                    }
                    if (data != null) {
                        val hexString = data.joinToString(" ") { "%02X".format(it) }
                        _messages.add("Received (hex): $hexString")
                    }
                }
                BluetoothConstants.ACTION_DEVICE_DISCOVERED,
                BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED -> {
                    val device: BluetoothDevice? = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        intent.getParcelableExtra("DEVICE", BluetoothDevice::class.java)
                    } else {
                        @Suppress("DEPRECATION")
                        intent.getParcelableExtra("DEVICE")
                    }
                    device?.let {
                        val deviceType = if (intent.action == BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED) DeviceType.BLE else DeviceType.CLASSIC
                        addDebugLog("Device discovered: ${it.name ?: "Unknown"} (${it.address}), type: $deviceType")
                        val wrapper = BluetoothDeviceWrapper(
                            device = it,
                            name = it.name ?: "Unknown",
                            address = it.address,
                            type = deviceType,
                            rssi = intent.getIntExtra("RSSI", 0).let { if (it == 0) null else it },
                            isBonded = it.bondState == BluetoothDevice.BOND_BONDED
                        )
                        if (!_discoveredDevices.any { d -> d.address == wrapper.address }) {
                            _discoveredDevices.add(wrapper)
                            addDebugLog("Added new device to list. Total devices: ${_discoveredDevices.size}")
                        } else {
                            addDebugLog("Device already in list, skipping duplicate")
                        }
                    }
                }
                BluetoothConstants.ACTION_DISCOVERY_FINISHED -> {
                    _isScanning.value = false
                    _scanType.value = ScanType.NONE
                }
                BluetoothConstants.ACTION_BLUETOOTH_ENABLED -> {
                    _isBluetoothEnabled.value = true
                }
                BluetoothConstants.ACTION_BLUETOOTH_DISABLED -> {
                    _isBluetoothEnabled.value = false
                }
            }
        }
    }

    init {
        _isBluetoothEnabled.value = bluetoothAdapter?.isEnabled == true
        val filter = IntentFilter().apply {
            addAction(BluetoothConstants.ACTION_CONNECTING)
            addAction(BluetoothConstants.ACTION_CONNECTED)
            addAction(BluetoothConstants.ACTION_DISCONNECTED)
            addAction(BluetoothConstants.ACTION_CONNECTION_FAILED)
            addAction(BluetoothConstants.ACTION_BLE_SERVICES_DISCOVERED)
            addAction(BluetoothConstants.ACTION_MESSAGE_RECEIVED)
            addAction(BluetoothConstants.ACTION_DEVICE_DISCOVERED)
            addAction(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED)
            addAction(BluetoothConstants.ACTION_DISCOVERY_FINISHED)
            addAction(BluetoothConstants.ACTION_BLUETOOTH_ENABLED)
            addAction(BluetoothConstants.ACTION_BLUETOOTH_DISABLED)
        }
        ContextCompat.registerReceiver(context, bluetoothReceiver, filter, ContextCompat.RECEIVER_NOT_EXPORTED)
        Intent(context, BluetoothService::class.java).also { intent ->
            context.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
        }
    }

    override fun onCleared() {
        super.onCleared()
        context.unregisterReceiver(bluetoothReceiver)
        context.unbindService(serviceConnection)
        stopScan()
    }

    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    fun startScan(scanType: ScanType) {
        addDebugLog("Starting scan with type: $scanType")
        if (_isScanning.value) {
            addDebugLog("Scan already in progress, ignoring request")
            return
        }
        _discoveredDevices.clear()
        _isScanning.value = true
        _scanType.value = scanType
        when (scanType) {
            ScanType.BLE -> {
                addDebugLog("Starting BLE scan")
                bleScanner.startScan()
            }
            ScanType.CLASSIC -> {
                addDebugLog("Starting Classic Bluetooth discovery")
                if (bluetoothAdapter?.startDiscovery() == false) {
                    addDebugLog("Failed to start Classic Bluetooth discovery")
                    _isScanning.value = false
                    _scanType.value = ScanType.NONE
                } else {
                    addDebugLog("Classic Bluetooth discovery started successfully")
                }
            }
            ScanType.NONE -> {
                addDebugLog("Scan type set to NONE")
                _isScanning.value = false
            }
        }
    }

    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    fun stopScan() {
        if (!_isScanning.value) return
        _isScanning.value = false
        when (_scanType.value) {
            ScanType.BLE -> bleScanner.stopScan()
            ScanType.CLASSIC -> bluetoothAdapter?.cancelDiscovery()
            ScanType.NONE -> {}
        }
        _scanType.value = ScanType.NONE
    }

    fun connect(device: BluetoothDeviceWrapper) {
        addDebugLog("Connecting to device: ${device.name} (${device.address}), type: ${device.type}")
        stopScan()
        if (device.type == DeviceType.BLE) {
            addDebugLog("Clearing GATT services for BLE connection")
            _gattServices.clear()
        }
        addDebugLog("Calling bluetoothService.connect()")
        bluetoothService?.connect(device.device, device.type == DeviceType.BLE)
        addDebugLog("Connection request sent to service")
    }

    fun disconnect() {
        bluetoothService?.disconnect()
    }

    fun sendMessage(message: String, serviceUuid: UUID? = null, characteristicUuid: UUID? = null) {
        addDebugLog("Sending message: '$message' (length: ${message.length})")
        if (serviceUuid != null && characteristicUuid != null) {
            addDebugLog("Using BLE service: $serviceUuid, characteristic: $characteristicUuid")
        }
        bluetoothService?.write(message.toByteArray(), serviceUuid, characteristicUuid)
        _messages.add("Sent: $message")
        addDebugLog("Message added to UI list")
    }

    fun readCharacteristic(characteristic: BluetoothGattCharacteristic) {
        bluetoothService?.readCharacteristic(characteristic)
    }

    fun writeCharacteristic(characteristic: BluetoothGattCharacteristic, value: String) {
        bluetoothService?.write(value.toByteArray(), characteristic.service.uuid, characteristic.uuid)
    }

    fun enableNotifications(characteristic: BluetoothGattCharacteristic) {
        bluetoothService?.enableNotifications(characteristic.service.uuid, characteristic.uuid)
    }

    fun disableNotifications(characteristic: BluetoothGattCharacteristic) {
        bluetoothService?.disableNotifications(characteristic.service.uuid, characteristic.uuid)
    }

    fun clearMessages() {
        _messages.clear()
    }

    fun addDebugLog(message: String) {
        val timestamp = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date())
        _debugLogs.add("[$timestamp] $message")
        if (_debugLogs.size > 50) {
            _debugLogs.removeAt(0)
        }
    }

    fun clearDebugLogs() {
        _debugLogs.clear()
    }

    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    fun enableBluetooth() {
        val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
        enableBtIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        context.startActivity(enableBtIntent)
    }
}
