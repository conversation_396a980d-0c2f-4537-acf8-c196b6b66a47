package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.app.Service
import android.bluetooth.*
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Binder
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.core.app.ActivityCompat
import dagger.hilt.android.AndroidEntryPoint
import java.io.IOException
import java.util.*
import java.util.concurrent.BlockingQueue
import java.util.concurrent.LinkedBlockingQueue

@AndroidEntryPoint
class BluetoothService : Service() {
    private val binder = LocalBinder()
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var classicConnection: ClassicConnection? = null
    private var bleConnection: BleConnection? = null

    companion object {
        const val TAG = "BluetoothService"
        val UUID_SPP: UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB")
    }

    inner class LocalBinder : Binder() {
        fun getService(): BluetoothService = this@BluetoothService
    }

    override fun onBind(intent: Intent): IBinder = binder

    override fun onCreate() {
        super.onCreate()
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
    }

    fun initialize(): Boolean {
        if (bluetoothAdapter == null) {
            broadcastError(BluetoothConstants.ERROR_BLUETOOTH_NOT_AVAILABLE)
            return false
        }
        if (!bluetoothAdapter!!.isEnabled) {
            broadcastError(BluetoothConstants.ERROR_BLUETOOTH_NOT_ENABLED)
            return false
        }
        return true
    }

    fun connect(device: BluetoothDevice, useBLE: Boolean) {
        Log.d(TAG, "Connect called - Device: ${device.name ?: "Unknown"} (${device.address}), useBLE: $useBLE")
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "BLUETOOTH_CONNECT permission not granted")
            broadcastError(BluetoothConstants.ERROR_MISSING_PERMISSION)
            return
        }
        Log.d(TAG, "BLUETOOTH_CONNECT permission granted, proceeding with connection")
        disconnect() // Disconnect any existing connection
        if (useBLE) {
            Log.d(TAG, "Initializing BLE connection")
            bleConnection = BleConnection(device)
            bleConnection?.connect()
        } else {
            Log.d(TAG, "Initializing Classic Bluetooth connection")
            classicConnection = ClassicConnection(device)
            classicConnection?.connect()
        }
    }

    fun write(bytes: ByteArray, serviceUuid: UUID? = null, characteristicUuid: UUID? = null) {
        classicConnection?.write(bytes)
        bleConnection?.write(bytes, serviceUuid, characteristicUuid)
    }

    fun readCharacteristic(characteristic: BluetoothGattCharacteristic) {
        bleConnection?.readCharacteristic(characteristic)
    }

    fun enableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
        bleConnection?.enableNotifications(serviceUuid, characteristicUuid)
    }

    fun disableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
        bleConnection?.disableNotifications(serviceUuid, characteristicUuid)
    }

    fun getSupportedGattServices(): List<BluetoothGattService>? {
        return bleConnection?.getSupportedGattServices()
    }

    fun disconnect() {
        classicConnection?.disconnect()
        bleConnection?.disconnect()
        classicConnection = null
        bleConnection = null
    }

    private fun broadcastUpdate(action: String) {
        sendBroadcast(Intent(action))
    }

    private fun broadcastUpdate(action: String, deviceAddress: String) {
        val intent = Intent(action).apply {
            putExtra(BluetoothConstants.EXTRA_DEVICE_ADDRESS, deviceAddress)
        }
        sendBroadcast(intent)
    }

    private fun broadcastMessage(message: String) {
        val intent = Intent(BluetoothConstants.ACTION_MESSAGE_RECEIVED).apply {
            putExtra(BluetoothConstants.EXTRA_MESSAGE, message)
        }
        sendBroadcast(intent)
    }

    private fun broadcastData(data: ByteArray) {
        val intent = Intent(BluetoothConstants.ACTION_MESSAGE_RECEIVED).apply {
            putExtra(BluetoothConstants.EXTRA_DATA, data)
        }
        sendBroadcast(intent)
    }

    private fun broadcastError(errorCode: Int) {
        val intent = Intent(BluetoothConstants.ACTION_CONNECTION_FAILED).apply {
            putExtra(BluetoothConstants.EXTRA_ERROR_CODE, errorCode)
            putExtra(BluetoothConstants.EXTRA_ERROR_MESSAGE, BluetoothConstants.ERROR_MESSAGES[errorCode])
        }
        sendBroadcast(intent)
    }

    private abstract inner class Connection(protected val device: BluetoothDevice) {
        protected val handler = Handler(Looper.getMainLooper())
        protected var connectionState = BluetoothConstants.STATE_NONE

        abstract fun connect()
        abstract fun disconnect()

        protected val timeoutRunnable = Runnable { 
            if (connectionState == BluetoothConstants.STATE_CONNECTING) {
                disconnect()
                broadcastError(BluetoothConstants.ERROR_CONNECTION_TIMEOUT)
            }
        }

        protected fun startTimeout() {
            handler.postDelayed(timeoutRunnable, BluetoothConstants.CONNECTION_TIMEOUT)
        }

        protected fun stopTimeout() {
            handler.removeCallbacks(timeoutRunnable)
        }
    }

    private inner class ClassicConnection(device: BluetoothDevice) : Connection(device) {
        private var socket: BluetoothSocket? = null
        private var connectedThread: ConnectedThread? = null

        override fun connect() {
            Log.d(TAG, "ClassicConnection.connect() called for device: ${device.name ?: "Unknown"} (${device.address})")
            if (connectionState != BluetoothConstants.STATE_NONE) {
                Log.w(TAG, "Connection attempt ignored - already in state: $connectionState")
                return
            }
            connectionState = BluetoothConstants.STATE_CONNECTING
            broadcastUpdate(BluetoothConstants.ACTION_CONNECTING, device.address)
            startTimeout()
            Log.d(TAG, "Starting connection thread")

            Thread {
                try {
                    Log.d(TAG, "Checking BLUETOOTH_CONNECT permission in connection thread")
                    if (ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                        Log.e(TAG, "BLUETOOTH_CONNECT permission not granted in connection thread")
                        broadcastError(BluetoothConstants.ERROR_MISSING_PERMISSION)
                        return@Thread
                    }
                    Log.d(TAG, "Creating RFCOMM socket with UUID: $UUID_SPP")
                    socket = device.createRfcommSocketToServiceRecord(UUID_SPP)
                    Log.d(TAG, "Socket created successfully: ${socket != null}")
                    Log.d(TAG, "Cancelling any ongoing discovery")
                    bluetoothAdapter?.cancelDiscovery()
                    Log.d(TAG, "Attempting socket connection...")
                    socket?.connect()
                    Log.d(TAG, "Socket connection successful")
                    stopTimeout()
                    connected(socket!!)
                } catch (e: IOException) {
                    Log.e(TAG, "Socket connection error", e)
                    Log.e(TAG, "Connection failed for device: ${device.name ?: "Unknown"} (${device.address})")
                    disconnect()
                    broadcastError(BluetoothConstants.ERROR_CONNECTION_FAILED)
                }
            }.start()
        }

        private fun connected(socket: BluetoothSocket) {
            connectionState = BluetoothConstants.STATE_CONNECTED
            connectedThread = ConnectedThread(socket)
            connectedThread?.start()
            broadcastUpdate(BluetoothConstants.ACTION_CONNECTED, device.address)
        }

        fun write(bytes: ByteArray) {
            connectedThread?.write(bytes)
        }

        override fun disconnect() {
            stopTimeout()
            if (connectionState == BluetoothConstants.STATE_NONE) return
            connectionState = BluetoothConstants.STATE_NONE
            connectedThread?.cancel()
            connectedThread = null
            try {
                socket?.close()
            } catch (e: IOException) {
                Log.e(TAG, "Could not close the client socket", e)
            }
            broadcastUpdate(BluetoothConstants.ACTION_DISCONNECTED)
        }

        private inner class ConnectedThread(private val mmSocket: BluetoothSocket) : Thread() {
            private val mmInStream = mmSocket.inputStream
            private val mmOutStream = mmSocket.outputStream
            private val messageQueue: BlockingQueue<ByteArray> = LinkedBlockingQueue()

            override fun run() {
                val buffer = ByteArray(1024)
                var bytes: Int
                // Keep listening to the InputStream until an exception occurs
                while (connectionState == BluetoothConstants.STATE_CONNECTED) {
                    try {
                        bytes = mmInStream.read(buffer)
                        val data = buffer.copyOf(bytes)
                        broadcastData(data)
                    } catch (e: IOException) {
                        Log.d(TAG, "Input stream was disconnected", e)
                        disconnect()
                        broadcastError(BluetoothConstants.ERROR_CONNECTION_LOST)
                        break
                    } catch (e: InterruptedException) {
                        break
                    }
                }
            }

            fun write(bytes: ByteArray) {
                Log.d(TAG, "Added message to queue, length: ${bytes.size} bytes")
                messageQueue.add(bytes)
            }

            fun cancel() {
                interrupt()
            }

            init {
                // Writer thread
                Thread {
                    Log.d(TAG, "Started message writer thread")
                    while (connectionState == BluetoothConstants.STATE_CONNECTED) {
                        try {
                            Log.d(TAG, "Waiting for message in queue...")
                            val bytes = messageQueue.take() // Blocks until a message is available
                            Log.d(TAG, "Writing ${bytes.size} bytes to output stream")
                            mmOutStream.write(bytes)
                            Log.d(TAG, "Message written successfully")
                            broadcastUpdate(BluetoothConstants.ACTION_MESSAGE_SENT)
                        } catch (e: InterruptedException) {
                            Log.d(TAG, "Writer thread interrupted")
                            break
                        } catch (e: IOException) {
                            Log.e(TAG, "Error occurred when sending data", e)
                            broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
                            break
                        }
                    }
                    Log.d(TAG, "Message writer thread ended")
                }.start()
            }
        }
    }

    private inner class BleConnection(device: BluetoothDevice) : Connection(device) {
        private var gatt: BluetoothGatt? = null

        private val gattCallback = object : BluetoothGattCallback() {
            override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
                if (ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                    broadcastError(BluetoothConstants.ERROR_MISSING_PERMISSION)
                    return
                }
                stopTimeout()
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    if (newState == BluetoothProfile.STATE_CONNECTED) {
                        connectionState = BluetoothConstants.STATE_CONNECTED
                        <EMAIL> = gatt
                        broadcastUpdate(BluetoothConstants.ACTION_CONNECTED, device.address)
                        gatt.discoverServices()
                    } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                        disconnect()
                    }
                } else {
                    disconnect()
                    broadcastError(BluetoothConstants.ERROR_CONNECTION_FAILED)
                }
            }

            override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    broadcastUpdate(BluetoothConstants.ACTION_BLE_SERVICES_DISCOVERED)
                } else {
                    disconnect()
                    broadcastError(BluetoothConstants.ERROR_BLE_SERVICES_NOT_FOUND)
                }
            }

            override fun onCharacteristicWrite(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    broadcastUpdate(BluetoothConstants.ACTION_MESSAGE_SENT)
                } else {
                    broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
                }
            }

            override fun onCharacteristicRead(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, status: Int) {
                if (status == BluetoothGatt.GATT_SUCCESS) {
                    characteristic.value?.let { data ->
                        broadcastData(data)
                    }
                } else {
                    broadcastError(BluetoothConstants.ERROR_BLE_SERVICES_NOT_FOUND)
                }
            }

            override fun onCharacteristicChanged(gatt: BluetoothGatt, characteristic: BluetoothGattCharacteristic, value: ByteArray) {
                broadcastData(value)
            }

            override fun onDescriptorWrite(gatt: BluetoothGatt, descriptor: BluetoothGattDescriptor, status: Int) {
                if (status != BluetoothGatt.GATT_SUCCESS) {
                    broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
                }
            }
        }

        override fun connect() {
            if (connectionState != BluetoothConstants.STATE_NONE) return
            if (ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                broadcastError(BluetoothConstants.ERROR_MISSING_PERMISSION)
                return
            }
            connectionState = BluetoothConstants.STATE_CONNECTING
            broadcastUpdate(BluetoothConstants.ACTION_CONNECTING, device.address)
            startTimeout()
            gatt = device.connectGatt(this@BluetoothService, false, gattCallback)
        }

        fun write(bytes: ByteArray, serviceUuid: UUID?, characteristicUuid: UUID?) {
            if (connectionState != BluetoothConstants.STATE_CONNECTED || serviceUuid == null || characteristicUuid == null) return
            val service = gatt?.getService(serviceUuid)
            val characteristic = service?.getCharacteristic(characteristicUuid)

            if (characteristic == null) {
                broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
                return
            }

            // Check if characteristic supports writing
            val canWrite = (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE) != 0 ||
                          (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0

            if (!canWrite) {
                broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
                return
            }

            if (ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                 broadcastError(BluetoothConstants.ERROR_MISSING_PERMISSION)
                return
            }

            // Set the value and write type
            characteristic.value = bytes
            characteristic.writeType = if ((characteristic.properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0) {
                BluetoothGattCharacteristic.WRITE_TYPE_NO_RESPONSE
            } else {
                BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
            }

            val writeResult = gatt?.writeCharacteristic(characteristic)
            if (writeResult != true) {
                broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
            }
        }

        fun readCharacteristic(characteristic: BluetoothGattCharacteristic) {
            if (ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                broadcastError(BluetoothConstants.ERROR_MISSING_PERMISSION)
                return
            }
            gatt?.readCharacteristic(characteristic)
        }

        fun enableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
            if (connectionState != BluetoothConstants.STATE_CONNECTED) return
            val service = gatt?.getService(serviceUuid)
            val characteristic = service?.getCharacteristic(characteristicUuid)

            if (characteristic == null) {
                broadcastError(BluetoothConstants.ERROR_BLE_SERVICES_NOT_FOUND)
                return
            }

            // Check if characteristic supports notifications or indications
            val canNotify = (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0 ||
                           (characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0

            if (!canNotify) {
                broadcastError(BluetoothConstants.ERROR_BLE_SERVICES_NOT_FOUND)
                return
            }

            if (ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                broadcastError(BluetoothConstants.ERROR_MISSING_PERMISSION)
                return
            }

            // Enable local notifications
            val notificationEnabled = gatt?.setCharacteristicNotification(characteristic, true)
            if (notificationEnabled != true) {
                broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
                return
            }

            // Write to the Client Characteristic Configuration Descriptor
            val cccdUuid = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            val descriptor = characteristic.getDescriptor(cccdUuid)

            if (descriptor != null) {
                val value = if ((characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                    BluetoothGattDescriptor.ENABLE_INDICATION_VALUE
                } else {
                    BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                }

                descriptor.value = value
                val writeResult = gatt?.writeDescriptor(descriptor)
                if (writeResult != true) {
                    broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
                }
            }
        }

        fun disableNotifications(serviceUuid: UUID, characteristicUuid: UUID) {
            if (connectionState != BluetoothConstants.STATE_CONNECTED) return
            val service = gatt?.getService(serviceUuid)
            val characteristic = service?.getCharacteristic(characteristicUuid)

            if (characteristic == null) {
                broadcastError(BluetoothConstants.ERROR_BLE_SERVICES_NOT_FOUND)
                return
            }

            if (ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                broadcastError(BluetoothConstants.ERROR_MISSING_PERMISSION)
                return
            }

            // Disable local notifications
            val notificationDisabled = gatt?.setCharacteristicNotification(characteristic, false)
            if (notificationDisabled != true) {
                broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
                return
            }

            // Write to the Client Characteristic Configuration Descriptor
            val cccdUuid = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")
            val descriptor = characteristic.getDescriptor(cccdUuid)

            if (descriptor != null) {
                descriptor.value = BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE
                val writeResult = gatt?.writeDescriptor(descriptor)
                if (writeResult != true) {
                    broadcastError(BluetoothConstants.ERROR_WRITE_FAILED)
                }
            }
        }

        fun getSupportedGattServices(): List<BluetoothGattService>? {
            return gatt?.services
        }

        override fun disconnect() {
            stopTimeout()
            if (connectionState == BluetoothConstants.STATE_NONE) return
            connectionState = BluetoothConstants.STATE_NONE
            if (ActivityCompat.checkSelfPermission(this@BluetoothService, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                broadcastError(BluetoothConstants.ERROR_MISSING_PERMISSION)
                return
            }
            gatt?.disconnect()
            gatt?.close()
            gatt = null
            broadcastUpdate(BluetoothConstants.ACTION_DISCONNECTED)
        }
    }
}
