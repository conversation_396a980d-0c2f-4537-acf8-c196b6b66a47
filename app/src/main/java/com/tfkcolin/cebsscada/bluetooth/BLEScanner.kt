package com.tfkcolin.cebsscada.bluetooth

import android.Manifest
import android.bluetooth.BluetoothAdapter
import android.bluetooth.le.BluetoothLeScanner
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanFilter
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.ParcelUuid
import androidx.annotation.RequiresPermission
import java.util.*

class BLEScanner(private val context: Context, private val debugLogger: ((String) -> Unit)? = null) {
    private var bluetoothAdapter: BluetoothAdapter? = null
    private var bluetoothLeScanner: BluetoothLeScanner? = null
    private var scanning = false
    private var scanCallback: ScanCallback? = null

    init {
        bluetoothAdapter = BluetoothAdapter.getDefaultAdapter()
        if (bluetoothAdapter != null) {
            bluetoothLeScanner = bluetoothAdapter!!.bluetoothLeScanner
        }
    }

    @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    fun startScan(serviceUUID: UUID? = null) {
        val message = "startScan called, scanning=$scanning"
        android.util.Log.d("BLEScanner", message)
        debugLogger?.invoke(message)
        if (scanning) {
            val alreadyScanningMessage = "Already scanning, returning"
            android.util.Log.d("BLEScanner", alreadyScanningMessage)
            debugLogger?.invoke(alreadyScanningMessage)
            return
        }

        if (bluetoothAdapter == null) {
            val adapterNullMessage = "BluetoothAdapter is null"
            android.util.Log.e("BLEScanner", adapterNullMessage)
            debugLogger?.invoke(adapterNullMessage)
            sendError(BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
            return
        }

        if (bluetoothLeScanner == null) {
            val scannerNullMessage = "BluetoothLeScanner is null, BLE not supported"
            android.util.Log.e("BLEScanner", scannerNullMessage)
            debugLogger?.invoke(scannerNullMessage)
            sendError(BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
            return
        }

        val isEnabled = bluetoothAdapter?.isEnabled ?: false
        val bluetoothEnabledMessage = "Bluetooth enabled: $isEnabled"
        android.util.Log.d("BLEScanner", bluetoothEnabledMessage)
        debugLogger?.invoke(bluetoothEnabledMessage)
        if (!isEnabled) {
            val bluetoothDisabledMessage = "Bluetooth is disabled, cannot start scan"
            android.util.Log.e("BLEScanner", bluetoothDisabledMessage)
            debugLogger?.invoke(bluetoothDisabledMessage)
            sendError(BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
            return
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val settingUpCallbackMessage = "Setting up ScanCallback"
            android.util.Log.d("BLEScanner", settingUpCallbackMessage)
            debugLogger?.invoke(settingUpCallbackMessage)
            scanCallback = object : ScanCallback() {
                override fun onScanResult(callbackType: Int, result: ScanResult) {
                    super.onScanResult(callbackType, result)
                    val scanResultMessage = "onScanResult: device=${result.device.address}, name=${result.device.name}, rssi=${result.rssi}"
                    android.util.Log.d("BLEScanner", scanResultMessage)
                    debugLogger?.invoke(scanResultMessage)
                    val intent = Intent(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED).apply {
                        putExtra("DEVICE", result.device)
                        putExtra("RSSI", result.rssi)
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            result.scanRecord?.let { scanRecord ->
                                putExtra("DEVICE_NAME", scanRecord.deviceName)
                            }
                        }
                    }
                    context.sendBroadcast(intent)
                }

                override fun onBatchScanResults(results: MutableList<ScanResult>) {
                    super.onBatchScanResults(results)
                    val batchResultsMessage = "onBatchScanResults: ${results.size} devices found"
                    android.util.Log.d("BLEScanner", batchResultsMessage)
                    debugLogger?.invoke(batchResultsMessage)
                    results.forEach { result ->
                        val batchResultMessage = "Batch result: device=${result.device.address}, name=${result.device.name}, rssi=${result.rssi}"
                        android.util.Log.d("BLEScanner", batchResultMessage)
                        debugLogger?.invoke(batchResultMessage)
                        val intent = Intent(BluetoothConstants.ACTION_BLE_DEVICE_DISCOVERED).apply {
                            putExtra("DEVICE", result.device)
                            putExtra("RSSI", result.rssi)
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                result.scanRecord?.let { scanRecord ->
                                    putExtra("DEVICE_NAME", scanRecord.deviceName)
                                }
                            }
                        }
                        context.sendBroadcast(intent)
                    }
                }

                override fun onScanFailed(errorCode: Int) {
                    super.onScanFailed(errorCode)
                    val scanFailedMessage = "onScanFailed: errorCode=$errorCode"
                    android.util.Log.e("BLEScanner", scanFailedMessage)
                    debugLogger?.invoke(scanFailedMessage)
                    sendError(BluetoothConstants.ERROR_BLE_NOT_SUPPORTED)
                }
            }

            val scanFilters = mutableListOf<ScanFilter>()
            serviceUUID?.let {
                scanFilters.add(ScanFilter.Builder().setServiceUuid(ParcelUuid(it)).build())
            }

            val scanSettings = ScanSettings.Builder()
                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
                .build()

            bluetoothLeScanner?.startScan(scanFilters, scanSettings, scanCallback)
            scanning = true
            val scanStartedMessage = "BLE scan started successfully"
            android.util.Log.d("BLEScanner", scanStartedMessage)
            debugLogger?.invoke(scanStartedMessage)

            // Schedule a status check after 5 seconds
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val statusCheckMessage = "Scan status check: scanning=$scanning"
                android.util.Log.d("BLEScanner", statusCheckMessage)
                debugLogger?.invoke(statusCheckMessage)
            }, 5000)
        }
    }

        @RequiresPermission(Manifest.permission.BLUETOOTH_SCAN)
    fun stopScan() {
        if (!scanning) return

        bluetoothLeScanner?.stopScan(scanCallback)
        scanCallback = null
        scanning = false
        
        val intent = Intent(BluetoothConstants.ACTION_DISCOVERY_FINISHED)
        context.sendBroadcast(intent)
    }

    fun isScanning(): Boolean = scanning

    private fun sendError(errorCode: Int) {
        val intent = Intent(BluetoothConstants.ACTION_CONNECTION_FAILED).apply {
            putExtra("ERROR_CODE", errorCode)
            putExtra("ERROR_MESSAGE", BluetoothConstants.ERROR_MESSAGES[errorCode] ?: "Unknown error")
        }
        context.sendBroadcast(intent)
    }
}
